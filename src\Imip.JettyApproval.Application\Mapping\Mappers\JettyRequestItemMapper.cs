using System;
using System.Collections.Generic;
using Imip.JettyApproval.JettyRequests;
using Riok.Mapperly.Abstractions;

namespace Imip.JettyApproval.Mapping.Mappers;

/// <summary>
/// Mapper for JettyRequestItem entity
/// </summary>
[Mapper]
public partial class JettyRequestItemMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(JettyRequestItem.Id), nameof(JettyRequestItemDto.Id))]
    [MapperIgnoreSource(nameof(JettyRequestItem.IsDeleted))]
    [MapperIgnoreSource(nameof(JettyRequestItem.DeleterId))]
    [MapperIgnoreSource(nameof(JettyRequestItem.DeletionTime))]
    [MapperIgnoreSource(nameof(JettyRequestItem.LastModificationTime))]
    [MapperIgnoreSource(nameof(JettyRequestItem.LastModifierId))]
    [MapperIgnoreSource(nameof(JettyRequestItem.CreationTime))]
    [MapperIgnoreSource(nameof(JettyRequestItem.CreatorId))]
    [MapperIgnoreSource(nameof(JettyRequestItem.ExtraProperties))]
    [MapperIgnoreSource(nameof(JettyRequestItem.ConcurrencyStamp))]
    public partial JettyRequestItemDto MapToDto(JettyRequestItem entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(JettyRequestItem.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(JettyRequestItem.JettyRequestId))] // Don't map JettyRequestId - it's set manually
    public partial void MapToEntity(CreateUpdateJettyRequestItemDto dto, JettyRequestItem entity);

    // Custom mapping methods for complex scenarios
    public JettyRequestItem CreateEntityWithId(CreateUpdateJettyRequestItemDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (JettyRequestItem)Activator.CreateInstance(typeof(JettyRequestItem), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        // Set the JettyRequestId manually if provided in the DTO
        if (dto.JettyRequestId.HasValue)
        {
            entity.JettyRequestId = dto.JettyRequestId.Value;
        }

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<JettyRequestItemDto> MapToDtoList(List<JettyRequestItem> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<JettyRequestItemDto> MapToDtoEnumerable(IEnumerable<JettyRequestItem> entities);
}