using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.JettyApproval.Approvals.ApprovalStages;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.JettyApproval.EntityFrameworkCore.Approvals;

public class ApprovalStageRepository : EfCoreRepository<JettyApprovalDbContext, ApprovalStage, Guid>, IApprovalStageRepository
{
    public ApprovalStageRepository(IDbContextProvider<JettyApprovalDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    public async Task<List<ApprovalStage>> GetByApprovalTemplateIdAsync(Guid approvalTemplateId)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<ApprovalStage>()
            .Where(x => x.ApprovalTemplateId == approvalTemplateId)
            .ToListAsync();
    }

    public async Task<List<ApprovalStage>> GetByApproverIdAsync(Guid approverId)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<ApprovalStage>()
            .Where(x => x.ApproverId == approverId)
            .ToListAsync();
    }

    public async Task<List<ApprovalStage>> GetByStatusAsync(string status)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<ApprovalStage>()
            .Where(x => x.Status == status)
            .ToListAsync();
    }

    public async Task<List<ApprovalStage>> GetByRequesterIdAsync(string requesterId)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.Set<ApprovalStage>()
            .Where(x => x.RequesterId == requesterId)
            .ToListAsync();
    }
}