using Imip.JettyApproval.Attachments;
using Imip.JettyApproval.Mapping.Mappers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.BlobStoring;

namespace Imip.JettyApproval.Attachments;

/// <summary>
/// Application service for Attachment entity
/// </summary>
public class AttachmentAppService :
    CrudAppService<Attachment, AttachmentDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateAttachmentDto, CreateUpdateAttachmentDto>,
    IAttachmentAppService
{
    private readonly IAttachmentRepository _attachmentRepository;
    private readonly AttachmentMapper _mapper;
    private readonly IBlobContainer _blobContainer;
    private readonly IConfiguration _configuration;

    private readonly ILogger<AttachmentAppService> _logger;

    public AttachmentAppService(
        IAttachmentRepository attachmentRepository,
        AttachmentMapper mapper,
        IBlobContainerFactory blobContainerFactory,
        IConfiguration configuration,
        ILogger<AttachmentAppService> logger)
        : base(attachmentRepository)
    {
        _attachmentRepository = attachmentRepository;
        _mapper = mapper;
        _blobContainer = blobContainerFactory.Create("default");
        _configuration = configuration;
        _logger = logger;
    }

    public override async Task<AttachmentDto> CreateAsync(CreateUpdateAttachmentDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        await _attachmentRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task<AttachmentDto> UpdateAsync(Guid id, CreateUpdateAttachmentDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _attachmentRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _attachmentRepository.UpdateAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _attachmentRepository.GetAsync(id);

        await _attachmentRepository.DeleteAsync(entity, autoSave: true);
    }

    public override async Task<AttachmentDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _attachmentRepository.GetAsync(id);
        return _mapper.MapToDto(entity);
    }

    public override async Task<PagedResultDto<AttachmentDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _attachmentRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<AttachmentDto>(totalCount, dtos);
    }

    /// <summary>
    /// Uploads a file
    /// </summary>
    public async Task<FileUploadResultDto> UploadFileAsync(FileUploadDto input, string fileName, string contentType, byte[] fileBytes)
    {
        // Validate input
        if (string.IsNullOrWhiteSpace(fileName))
        {
            throw new UserFriendlyException(L["FileNameCannotBeEmpty"]);
        }

        if (fileBytes == null || fileBytes.Length == 0)
        {
            throw new UserFriendlyException(L["FileContentCannotBeEmpty"]);
        }

        try
        {
            var fileSize = fileBytes.Length;

            // Generate a unique blob name
            var blobName = $"{Guid.NewGuid():N}_{Path.GetFileName(fileName)}";

            // Save the file to blob storage
            using (var memoryStream = new MemoryStream(fileBytes))
            {
                await _blobContainer.SaveAsync(blobName, memoryStream);
            }

            // Create an attachment entity
            var attachment = new Attachment(
                GuidGenerator.Create(),
                fileName,
                contentType,
                fileSize,
                blobName,
                input.Description,
                input.ReferenceId,
                input.ReferenceType
            );

            // Save the attachment metadata to the database
            await _attachmentRepository.InsertAsync(attachment);

            // Return the result
            return new FileUploadResultDto
            {
                Id = attachment.Id,
                FileName = attachment.FileName ?? "unknown_file",
                ContentType = attachment.ContentType ?? "application/octet-stream",
                Size = attachment.Size,
                UploadTime = attachment.CreationTime,
                Url = GetFileUrl(attachment.Id),
                StreamUrl = GetFileStreamUrl(attachment.Id)
            };
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException(L["FailedToUploadFile"], ex.Message);
        }
    }

    /// <summary>
    /// Gets the download URL for a file
    /// </summary>
    private string GetFileUrl(Guid id)
    {
        // Get the base URL from configuration
        var baseUrl = _configuration["App:ServerRootAddress"]?.TrimEnd('/') ?? "";

        // Return the URL
        return $"{baseUrl}/api/attachment/download/{id}";
    }

    /// <summary>
    /// Gets the stream URL for a file
    /// </summary>
    private string GetFileStreamUrl(Guid id)
    {
        // Get the base URL from configuration
        var baseUrl = _configuration["App:ServerRootAddress"]?.TrimEnd('/') ?? "";

        // Return the URL
        return $"{baseUrl}/api/attachment/stream/{id}";
    }

}