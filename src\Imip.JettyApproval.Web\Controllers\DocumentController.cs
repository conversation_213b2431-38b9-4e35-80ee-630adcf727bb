using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.JettyApproval.Documents;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.JettyApproval.Web.Controllers;

[RemoteService]
[Route("api/idjas/document")]
[Authorize]
public class DocumentController : AbpController
{
    private readonly IDocumentService _documentService;

    public DocumentController(IDocumentService documentService)
    {
        _documentService = documentService;
    }

    [HttpPost]
    [Route("templates/upload")]
    [Consumes("multipart/form-data")]
    public async Task<ActionResult<DocumentTemplateDto>> UploadTemplateAsync([FromForm] UploadDocumentTemplateDto input)
    {
        if (input.File == null || input.File.Length == 0)
        {
            return BadRequest("No file was uploaded");
        }

        var template = await _documentService.UploadTemplateAsync(input);
        return Ok(template);
    }
}
