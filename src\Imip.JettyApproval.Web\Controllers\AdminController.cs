using InertiaCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.JettyApproval.Web.Controllers;

[Authorize]
public class AdminController : AbpController
{
    [HttpGet("/")]
    public async Task<IActionResult> Index()
    {
        await Task.CompletedTask;
        return Inertia.Render("home", new
        {
            message = "Welcome to Imip.Ekb Dashboard",
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/admin")]
    public async Task<IActionResult> AdminPage()
    {
        await Task.CompletedTask;
        return Inertia.Render("home", new
        {
            message = "Welcome to Imip.Ekb Dashboard",
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/application")]
    public async Task<IActionResult> ApplicationPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("application/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/application/create")]
    public async Task<IActionResult> ApplicationCreatePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("application/create/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/application/status")]
    public async Task<IActionResult> ApplicationStatusCreatePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("application/status/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/application/list")]
    public async Task<IActionResult> ApplicationListPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("application/list/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/application/draft")]
    public async Task<IActionResult> ApplicationDraftCreatePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("application/draft/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/application/{id}/edit")]
    public async Task<IActionResult> ApplicationEditPage(Guid id)
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("application/edit/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            id,
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/approval")]
    public async Task<IActionResult> ApprovalPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("approval/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/approval/history")]
    public async Task<IActionResult> ApprovalHistoryPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("approval/history/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    //[HttpGet("/approval/{id}")]
    //public async Task<IActionResult> ApprovalEditPage(Guid id)
    //{
    //    await Task.CompletedTask; // Placeholder for future async operations

    //    return Inertia.Render("approval/edit", new
    //    {
    //        timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
    //        user = CurrentUser?.UserName ?? "Anonymous",
    //        id,
    //        isAuthenticated = User?.Identity?.IsAuthenticated ?? false
    //    });
    //}

    [HttpGet("/jetty")]
    public async Task<IActionResult> JettyPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("jetty/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/jetty/schedule")]
    public async Task<IActionResult> JettySchedulePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("jetty/schedule/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/jetty/docked-vessel")]
    public async Task<IActionResult> JettyDockedVesselPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("jetty/docked-vessel/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/settings")]
    public async Task<IActionResult> SettingPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("setting/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/report")]
    public async Task<IActionResult> ReportPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("report/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/approval-template")]
    public async Task<IActionResult> ApprovalTemplatePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("master/approval-template/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/document-template")]
    public async Task<IActionResult> DocumentTemplatePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("master/document-template/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }
}