import { postApiIdentityServerUserQuery, postApiIdjasApprovalTemplate, putApiIdjasApprovalTemplateById } from "@/client/sdk.gen";
import type { ApprovalTemplateDto, CreateUpdateApprovalApproverDto, CreateUpdateApprovalCriteriaDto, CreateUpdateApprovalStageDto, FilterOperator, LogicalOperator, PagedResultDtoOfExtendedIdentityUserDto, RemoteServiceErrorResponse } from "@/client/types.gen";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import type { MultiSelectOption } from "@/components/ui/multi-select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/lib/useToast";
import { useMutation } from "@tanstack/react-query";
import React, { useEffect } from "react";
import { FormProvider, useFieldArray, useForm } from "react-hook-form";
import ApproversTab from "./approval-template-approvers-tab";
import CriteriasTab from "./approval-template-criterias-tab";

export interface ApprovalTemplateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  template: ApprovalTemplateDto | null;
}

export type FormValues = {
  name: string;
  description?: string;
  code?: string;
  approvers: CreateUpdateApprovalApproverDto[];
  criterias: CreateUpdateApprovalCriteriaDto[];
  stages: CreateUpdateApprovalStageDto[];
};

const ApprovalTemplateDialog: React.FC<ApprovalTemplateDialogProps> = ({ open, onOpenChange, template }) => {
  const methods = useForm<FormValues>({
    defaultValues: {
      name: template?.name ?? "",
      description: template?.description ?? "",
      code: template?.code ?? "",
      approvers: template?.approvers ?? [],
      criterias: template?.criterias ?? [],
      stages: template?.stages ?? [],
    },
  });
  const { register, handleSubmit, reset, control, formState: { errors, isDirty, isSubmitting }, setValue, watch } = methods;
  const { toast } = useToast()

  // Field arrays for tabs
  const approversArray = useFieldArray({ control, name: "approvers" });
  const criteriasArray = useFieldArray({ control, name: "criterias" });
  // const stagesArray = useFieldArray({ control, name: "stages" });

  // Reset form when dialog opens/closes or template changes
  useEffect(() => {
    reset({
      name: template?.name ?? "",
      description: template?.description ?? "",
      code: template?.code ?? "",
      approvers: template?.approvers ?? [],
      criterias: template?.criterias ?? [],
      stages: template?.stages ?? [],
    });
  }, [template, open, reset]);

  // Mutation for create
  const createMutation = useMutation({
    mutationFn: async (values: FormValues) => {
      return postApiIdjasApprovalTemplate({ body: values });
    },
    onSuccess: () => {
      onOpenChange(false);
      reset();
    },
    onError: (err: RemoteServiceErrorResponse) => {
      toast({
        title: err.error?.message || 'Error',
        description: err.error?.details || 'Failed to create approval template.',
        variant: 'error',
      })
    }
  });

  // Mutation for update
  const updateMutation = useMutation({
    mutationFn: async (values: FormValues) => {
      if (!template?.id) throw new Error("No template id");
      return putApiIdjasApprovalTemplateById({
        path: { id: template.id },
        body: values,
      });
    },
    onSuccess: () => {
      onOpenChange(false);
      reset();
    },
  });

  const onSubmit = (values: FormValues) => {
    // Remove approvalId from approvers and criterias before submit
    const cleanValues: FormValues = {
      ...values,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      approvers: values.approvers.map(({ approvalId, ...rest }) => rest),
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      criterias: values.criterias.map(({ approvalId, ...rest }) => rest),
      stages: values.stages,
    };
    if (template?.id) {
      updateMutation.mutate(cleanValues);
    } else {
      createMutation.mutate(cleanValues);
    }
  };

  const isLoading = isSubmitting || createMutation.isPending || updateMutation.isPending;
  const error = createMutation.error || updateMutation.error;

  const [searchValue, setSearchValue] = React.useState("");
  const [userOptions, setUserOptions] = React.useState<MultiSelectOption[]>([]);
  const userSearchMutation = useMutation<PagedResultDtoOfExtendedIdentityUserDto, Error, string>({
    mutationFn: async (keyword: string) => {
      const filterGroup = keyword
        ? {
            operator: "Or" as LogicalOperator,
            conditions: [
              { fieldName: "name", operator: "Contains" as FilterOperator, value: keyword },
              { fieldName: "userName", operator: "Contains" as FilterOperator, value: keyword },
              { fieldName: "email", operator: "Contains" as FilterOperator, value: keyword },
            ],
          }
        : undefined;
      const res = await postApiIdentityServerUserQuery({
        body: {
          maxResultCount: 20,
          skipCount: 0,
          filterGroup,
        },
      });
      return res?.data ?? { items: [], totalCount: 0 };
    },
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent size="2xl">
        <DialogHeader>
          <DialogTitle>Approval Template</DialogTitle>
          <DialogDescription>
            Fill in the details for the approval template.
          </DialogDescription>
        </DialogHeader>
        <FormProvider {...methods}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">Name <span className="text-destructive">*</span></label>
                <Input
                  {...register("name", { required: "Name is required" })}
                  className="input input-bordered w-full"
                  autoFocus
                  aria-invalid={!!errors.name}
                />
                {errors.name && <div className="text-xs text-destructive mt-1">{errors.name.message}</div>}
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Code</label>
                <Input
                  {...register("code")}
                  className="input input-bordered w-full"
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-1">Description</label>
                <Textarea
                  {...register("description")}
                  className="input input-bordered w-full min-h-[60px]"
                />
              </div>
            </div>
            <Tabs defaultValue="approvers" className="w-full">
              <TabsList className="mb-2">
                <TabsTrigger value="approvers">Approvers</TabsTrigger>
                <TabsTrigger value="criterias">Criterias</TabsTrigger>
                {/* <TabsTrigger value="stages">Stages</TabsTrigger> */}
              </TabsList>
              <TabsContent value="approvers">
                <ApproversTab
                  {...approversArray}
                  control={control}
                  setValue={setValue}
                  watch={watch}
                  searchValue={searchValue}
                  setSearchValue={setSearchValue}
                  userOptions={userOptions}
                  setUserOptions={setUserOptions}
                  userSearchMutation={userSearchMutation}
                />
              </TabsContent>
              <TabsContent value="criterias">
                <CriteriasTab {...criteriasArray} />
              </TabsContent>
              {/* <TabsContent value="stages">
                <StagesTab {...stagesArray} />
              </TabsContent> */}
            </Tabs>
            {error && <div className="text-xs text-destructive mt-2">{String(error instanceof Error ? error.message : error)}</div>}
            <DialogFooter className="mt-6">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit" className="ml-2" disabled={isLoading || !isDirty}>
                {isLoading ? "Saving..." : "Save"}
              </Button>
            </DialogFooter>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
};

export default ApprovalTemplateDialog; 