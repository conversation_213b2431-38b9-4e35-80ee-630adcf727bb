import Handsontable from 'handsontable';
import type { TableRowData } from './handsontable-column';

export const renderPreviewButton = (tableData: TableRowData[]) => (
  instance: Handsontable.Core | undefined,
  td: HTMLTableCellElement,
  _row: number,
  _col: number,
  _prop: string | number,
  _value: unknown,
  _cellProperties: Handsontable.CellProperties
) => {
  void _col;
  void _prop;
  void _value;
  void _cellProperties;
  const rowData = tableData[_row];
  if (rowData && rowData.status === 'Draft') {
    td.innerHTML = '';
    return;
  }
  const previewButton = `<button class="px-2 py-0.5 bg-blue-500 text-white rounded-md text-xs hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50" data-row="${_row}" data-action="preview">Preview</button>`;
  td.innerHTML = previewButton;
  const previewBtn = td.querySelector('[data-action="preview"]');
  if (previewBtn) {
    previewBtn.addEventListener('click', () => {
      // Implement preview logic if needed
    });
  }
};

export const renderSubmitButton = (tableData: TableRowData[]) => (
  instance: Handsontable.Core | undefined,
  td: HTMLTableCellElement,
  _row: number,
  _col: number,
  _prop: string | number,
  _value: unknown,
  _cellProperties: Handsontable.CellProperties
) => {
  void _col;
  void _prop;
  void _value;
  void _cellProperties;
  const rowData = tableData[_row];
  if (rowData && rowData.status === 'Draft') {
    td.innerHTML = '';
    return;
  }
  const submitButton = `<button class="px-2 py-0.5 bg-green-500 text-white rounded-md text-xs hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50" data-row="${_row}" data-action="submit">Submit</button>`;
  td.innerHTML = submitButton;
  const submitBtn = td.querySelector('[data-action="submit"]');
  if (submitBtn) {
    submitBtn.addEventListener('click', () => {
      // Implement submit logic if needed
    });
  }
};

export const renderDeleteButton = (tableData: TableRowData[], setTableData: (rows: TableRowData[]) => void) => (
  instance: Handsontable.Core | undefined,
  td: HTMLTableCellElement,
  _row: number,
  _col: number,
  _prop: string | number,
  _value: unknown,
  _cellProperties: Handsontable.CellProperties
) => {
  void _col;
  void _prop;
  void _value;
  void _cellProperties;
  const rowData = tableData[_row];
  if (rowData && rowData.status === 'Draft') {
    td.innerHTML = '';
    return;
  }
  const deleteButton = `<button class="px-2 py-0.5 bg-red-500 text-white rounded-md text-xs hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50" data-row="${_row}" data-action="delete">Delete</button>`;
  td.innerHTML = deleteButton;
  const deleteBtn = td.querySelector('[data-action="delete"]');
  if (deleteBtn) {
    deleteBtn.addEventListener('click', () => {
      const newTableData = [...tableData];
      newTableData.splice(_row, 1);
      setTableData(newTableData);
    });
  }
};
