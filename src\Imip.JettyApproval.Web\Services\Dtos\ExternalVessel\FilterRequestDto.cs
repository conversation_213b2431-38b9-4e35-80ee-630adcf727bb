using System;
using System.Collections.Generic;
using Imip.JettyApproval.Models;
using Volo.Abp.Application.Dtos;

namespace Imip.JettyApproval.Web.Services.Dtos.ExternalVessel;

public class FilterRequestDto
{
    public int MaxResultCount { get; set; }
    public int SkipCount { get; set; }
    public FilterGroup? FilterGroup { get; set; }
}

public class VesselListRequestDto
{
    public string? VesselType { get; set; } // "Import", "Export", "LocalIn", "LocalOut"
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
    public string? VesselName { get; set; }
    public string? Voyage { get; set; }
    public string? TenantName { get; set; }
    public FilterGroup? FilterGroup { get; set; }
}


public class VesselHeaderDto : EntityDto<Guid>
{
    public int DocEntry { get; set; }
    public string? VesselName { get; set; }
    public string? Voyage { get; set; }
    public DateTime? VesselArrival { get; set; }
    public DateTime? VesselDeparture { get; set; }
    public string VesselType { get; set; } = string.Empty; // "Import", "Export", "LocalIn", "LocalOut"
    public List<VesselItemDto> Items { get; set; } = new();
    public CargoShortDto? Cargo { get; set; }
    public CargoShortDto? Barge { get; set; }
    public JettyShortDto? Jetty { get; set; }

    // Extra columns
    public string? PortOrigin { get; set; }
    public string? DestinationPort { get; set; }
    public DateTime? BerthingDate { get; set; }
    public DateTime? AnchorageDate { get; set; }
    public DateTime? UnloadingDate { get; set; }
    public DateTime? FinishUnloadingDate { get; set; }
    public decimal? GrtWeight { get; set; }
    public string? AgentName { get; set; }
}

public class CargoShortDto
{
    public int DocEntry { get; set; }
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Alias { get; set; }
    public string? Type { get; set; }
    public decimal? GrossWeight { get; set; }
}

public class JettyShortDto
{
    public int DocEntry { get; set; }
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Alias { get; set; }
    public string? Port { get; set; }
    public decimal? Max { get; set; }
}


public class VesselItemDto : EntityDto<Guid>
{
    public int DocEntry { get; set; }
    public int DocNum { get; set; }
    public string? TenantName { get; set; }
    public string? ItemName { get; set; }
    public decimal? ItemQty { get; set; }
    public string? UnitQty { get; set; }
    public string? Cargo { get; set; }
    public string? Shipment { get; set; }
    public string? Remarks { get; set; }
    public string VesselType { get; set; } = string.Empty; // "Import", "Export", "LocalIn", "LocalOut"
    public TenantShortDto? Tenant { get; set; }
}

public class TenantShortDto
{
    public int DocEntry { get; set; }
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? FullName { get; set; }
}
