using System;
using System.Collections.Generic;
using Imip.JettyApproval.JettyRequests;
using Riok.Mapperly.Abstractions;

namespace Imip.JettyApproval.Mapping.Mappers;

/// <summary>
/// Mapper for JettyRequest entity
/// </summary>
[Mapper]
public partial class JettyRequestMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(JettyRequest.Id), nameof(JettyRequestDto.Id))]
    [MapperIgnoreSource(nameof(JettyRequest.IsDeleted))]
    [MapperIgnoreSource(nameof(JettyRequest.DeleterId))]
    [MapperIgnoreSource(nameof(JettyRequest.DeletionTime))]
    [MapperIgnoreSource(nameof(JettyRequest.LastModificationTime))]
    [MapperIgnoreSource(nameof(JettyRequest.LastModifierId))]
    [MapperIgnoreSource(nameof(JettyRequest.CreationTime))]
    [MapperIgnoreSource(nameof(JettyRequest.CreatorId))]
    [MapperIgnoreSource(nameof(JettyRequest.ExtraProperties))]
    [MapperIgnoreSource(nameof(JettyRequest.ConcurrencyStamp))]
    public partial JettyRequestDto MapToDto(JettyRequest entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(JettyRequest.Id))] // Don't change existing Id
    [MapperIgnoreTarget(nameof(JettyRequest.Items))] // Don't map items - they're handled separately
    public partial void MapToEntity(CreateUpdateJettyRequestDto dto, JettyRequest entity);

    // Custom mapping methods for complex scenarios
    public JettyRequest CreateEntityWithId(CreateUpdateJettyRequestDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (JettyRequest)Activator.CreateInstance(typeof(JettyRequest), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<JettyRequestDto> MapToDtoList(List<JettyRequest> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<JettyRequestDto> MapToDtoEnumerable(IEnumerable<JettyRequest> entities);
}