﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Imip.JettyApproval</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Imip.JettyApproval.Domain.Shared\Imip.JettyApproval.Domain.Shared.csproj" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Identity.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.Account.Application.Contracts" Version="9.1.1" />
    <PackageReference Include="Volo.Abp.TenantManagement.Application.Contracts" Version="9.1.1" />
  </ItemGroup>

</Project>
