import type { CreateUpdateJettyRequestDto, VesselHeaderDto } from "@/client";
import type { FieldErrors, UseFormRegister } from "react-hook-form";

export interface ApplicationFormProps {
  docNum: string;
  vesselType: string;
  vessel: VesselHeaderDto | null;
  voyage: string;
  jetty: string;
  arrivalDate: string;
  departureDate: string;
  asideDate: string;
  castOfDate: string;
  postDate: string;
  onDocNumChange: (value: string) => void;
  onVesselTypeChange: (value: string) => void;
  onVesselChange: (vessel: VesselHeaderDto | null) => void;
  onVoyageChange: (value: string) => void;
  onJettyChange: (value: string) => void;
  onArrivalDateChange: (value: string) => void;
  onDepartureDateChange: (value: string) => void;
  onAsideDateChange: (value: string) => void;
  onCastOfDateChange: (value: string) => void;
  onPostDateChange: (value: string) => void;
  errors?: FieldErrors<CreateUpdateJettyRequestDto>;
  register?: UseFormRegister<CreateUpdateJettyRequestDto>;
  title?: string;
}