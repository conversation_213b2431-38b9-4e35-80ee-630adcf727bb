import { FormField, FormSection } from '@/components/ui/FormField';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import React from 'react';
import { JettySelector } from './jetty-selector';
import type { ApplicationFormProps } from './types';
import VesselTable from './vessel-table';

const ApplicationForm: React.FC<ApplicationFormProps> = ({
  docNum,
  vesselType,
  vessel,
  voyage,
  jetty,
  arrivalDate,
  departureDate,
  asideDate,
  castOfDate,
  postDate,
  onDocNumChange,
  onVesselTypeChange,
  onVesselChange,
  onVoyageChange,
  onJettyChange,
  onArrivalDateChange,
  onDepartureDateChange,
  onAsideDateChange,
  onCastOfDateChange,
  onPostDateChange,
  errors = {},
  register,
  title = 'Create Application',
}) => {
  return (
    <>
      <div className="mb-6">
        <h2 className="text-lg font-bold text-gray-800 dark:text-white">{title}</h2>
        <div className="h-1 w-16 bg-primary rounded mt-2 mb-4" />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-2">
        <FormSection>
          <FormField label="DocNum">
            {register ? (
              <Input id="docNum" {...register('docNum')} />
            ) : (
              <Input id="docNum" value={docNum} onChange={(e) => onDocNumChange(e.target.value)} />
            )}
            {errors.docNum && (
              <span className="text-red-500 text-xs">{errors.docNum.message as string}</span>
            )}
          </FormField>
          <FormField label="Vessel Type">
            <Select value={vesselType} onValueChange={onVesselTypeChange}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select Vessel Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Import">Import</SelectItem>
                <SelectItem value="Export">Export</SelectItem>
                <SelectItem value="LocalIn">Local In</SelectItem>
                <SelectItem value="LocalOut">Local Out</SelectItem>
              </SelectContent>
            </Select>
            {errors.vesselType && (
              <span className="text-red-500 text-xs">{errors.vesselType.message as string}</span>
            )}
          </FormField>
          <FormField label="Vessel">
            <VesselTable
              vesselType={vesselType}
              selectedVessel={vessel}
              onVesselSelect={onVesselChange}
            />
            {errors.vesselName && (
              <span className="text-red-500 text-xs">{errors.vesselName.message as string}</span>
            )}
          </FormField>
          <FormField label="Voyage">
            {register ? (
              <Input id="voyage" {...register('voyage')} />
            ) : (
              <Input id="voyage" value={voyage} onChange={(e) => onVoyageChange(e.target.value)} />
            )}
            {errors.voyage && (
              <span className="text-red-500 text-xs">{errors.voyage.message as string}</span>
            )}
          </FormField>
          <FormField label="Jetty">
            <JettySelector
              value={jetty}
              onValueChange={onJettyChange}
              placeholder="Select Jetty"
            />
            {errors.jetty && (
              <span className="text-red-500 text-xs">{errors.jetty.message as string}</span>
            )}
          </FormField>
        </FormSection>

        <FormSection>
          <FormField label="Arrival Date & Time">
            {register ? (
              <Input id="arrivalDate" type="datetime-local" {...register('arrivalDate')} />
            ) : (
              <Input id="arrivalDate" type="datetime-local" value={arrivalDate} onChange={e => onArrivalDateChange(e.target.value)} />
            )}
            {errors.arrivalDate && (
              <span className="text-red-500 text-xs">{errors.arrivalDate.message as string}</span>
            )}
          </FormField>
          <FormField label="Departure Date & Time">
            {register ? (
              <Input id="departureDate" type="datetime-local" {...register('departureDate')} />
            ) : (
              <Input id="departureDate" type="datetime-local" value={departureDate} onChange={e => onDepartureDateChange(e.target.value)} />
            )}
            {errors.departureDate && (
              <span className="text-red-500 text-xs">{errors.departureDate.message as string}</span>
            )}
          </FormField>
          <FormField label="A/Side Date & Time">
            <Input id="asideDate" type="datetime-local" value={asideDate} onChange={e => onAsideDateChange(e.target.value)} />
          </FormField>
          <FormField label="Cast Of Date & Time">
            <Input id="castOfDate" type="datetime-local" value={castOfDate} onChange={e => onCastOfDateChange(e.target.value)} />
          </FormField>
          <FormField label="Posting Date & Time">
            <Input id="postingDate" type="date" value={postDate} onChange={e => onPostDateChange(e.target.value)} />
          </FormField>
        </FormSection>
      </div>
    </>
  );
};

export default ApplicationForm; 