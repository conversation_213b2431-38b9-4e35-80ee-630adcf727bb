export const columns = [
  { data: 'tenantName', type: 'text', title: 'Tenant', width: "10" },
  { data: 'itemName', type: 'text', title: 'Item Name', width: "40", wordWrap: false },
  { data: 'quantity', type: 'numeric', title: 'Quantity', width: "10" },
  { data: 'uom', type: 'text', title: 'UoM', width: "15" },
  { data: 'remark', type: 'text', title: 'Remark', width: "20" },
  { data: 'status', type: 'text', title: 'Status', width: "8", readOnly: true },
  { data: 'preview', title: 'Preview', width: "8", readOnly: true, filterable: false },
  { data: 'submit', title: 'Submit', width: "8", readOnly: true, filterable: false },
  { data: 'delete', title: 'Delete', width: "8", readOnly: true, filterable: false },
];

export type TableRowData = {
  tenantName: string;
  itemName: string;
  quantity: string;
  uom: string;
  remark: string;
  status: string;
  preview: string;
  submit: string;
  delete: string;
};