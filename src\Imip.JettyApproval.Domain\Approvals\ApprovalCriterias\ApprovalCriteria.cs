using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.JettyApproval.Approvals.ApprovalCriterias;

/// <summary>
/// Entity for storing approval criteria information
/// </summary>
public class ApprovalCriteria : FullAuditedAggregateRoot<Guid>
{
    /// <summary>
    /// Foreign key to the approval template
    /// </summary>
    public Guid ApprovalId { get; set; }

    /// <summary>
    /// Foreign key to the approval template (for EF Core relationship)
    /// </summary>
    public Guid ApprovalTemplateId { get; set; }

    /// <summary>
    /// Type of document for approval criteria
    /// </summary>
    public string? DocumentType { get; set; }

    /// <summary>
    /// Navigation property to the approval template
    /// </summary>
    public virtual ApprovalTemplates.ApprovalTemplate ApprovalTemplate { get; set; } = null!;

    /// <summary>
    /// Default constructor for EF Core
    /// </summary>
    protected ApprovalCriteria()
    {
    }

    /// <summary>
    /// Creates a new ApprovalCriteria
    /// </summary>
    public ApprovalCriteria(
        Guid id,
        Guid approvalId,
        string? documentType = null)
        : base(id)
    {
        ApprovalId = approvalId;
        ApprovalTemplateId = approvalId; // Keep both properties in sync
        DocumentType = documentType;
    }

    /// <summary>
    /// Creates a new ApprovalCriteria without ID (for mapping from DTOs)
    /// </summary>
    public ApprovalCriteria(
        Guid approvalId,
        string? documentType = null)
    {
        ApprovalId = approvalId;
        ApprovalTemplateId = approvalId; // Keep both properties in sync
        DocumentType = documentType;
    }
}