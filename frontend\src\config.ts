import {
  IconAppWindow,
  IconDashboard,
  IconShip,
  IconClipboardCheck,
  IconDatabase,
  IconReportAnalytics,
} from '@tabler/icons-react'
import type { Policy } from '@/lib/hooks/useGrantedPolicies'

export const siteConfig = {
  name: 'JETTY APPROVAL',
  baseLinks: {
    login: '/auth/login',
  },
}

export type siteConfig = typeof siteConfig

// export const clientConfig = {
//   url: process.env.NEXT_PUBLIC_API_URL,
//   audience: process.env.NEXT_PUBLIC_API_URL,
//   client_id: process.env.NEXT_PUBLIC_CLIENT_ID,
//   client_secret: process.env.NEXT_PUBLIC_CLIENT_SECRET,
//   scope: process.env.NEXT_PUBLIC_SCOPE,
//   redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}/auth/openiddict`,
//   post_logout_redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}`,
//   response_type: 'code',
//   grant_type: 'authorization_code',
//   post_login_route: `${process.env.NEXT_PUBLIC_APP_URL}/admin`,
//   code_challenge_method: 'S256',
// }

/**
 * List of menus shown in the Admin layout.
 * Each menu item contains a title, url, icon, and optional permission.
 */
export const AdminMenus: Array<{
  title: string;
  url: string;
  isActive: boolean;
  icon: React.ComponentType;
  items?: Array<{
    title: string;
    url: string;
    permission: Policy;
  }>,
  permission?: Policy;
}> = [
    {
      title: 'Dashboard',
      url: '/',
      isActive: false,
      icon: IconDashboard,
    },
    {
      title: 'Jetty Application',
      url: '#',
      isActive: false,
      icon: IconAppWindow,
      permission: 'IdentityServer.OpenIddictApplications',
      items: [
        {
          title: 'New Application',
          url: '/application/create',
          permission: 'IdentityServer.OpenIddictApplications',
        },
        // {
        //   title: 'History',
        //   url: '/application',
        //   permission: 'IdentityServer.OpenIddictScopes',
        // },
        {
          title: 'Application List',
          url: '/application/list',
          permission: 'IdentityServer.OpenIddictApplications',
        },
      ],
    },
    {
      title: 'Approval Management',
      url: '#',
      isActive: false,
      icon: IconClipboardCheck,
      permission: 'IdentityServer.OpenIddictApplications',
      items: [
        {
          title: 'Incoming Approval',
          url: '/approval',
          permission: 'IdentityServer.OpenIddictApplications',
        },
        {
          title: 'History',
          url: '/approval/history',
          permission: 'IdentityServer.OpenIddictScopes',
        },
      ],
    },
    {
      title: 'Jetty Operations',
      url: '#',
      isActive: false,
      icon: IconShip,
      permission: 'IdentityServer.OpenIddictApplications',
      items: [
        {
          title: 'Jetty Schedule',
          url: '/jetty/schedule',
          permission: 'IdentityServer.OpenIddictApplications',
        },
        {
          title: 'Docked Vessel List',
          url: '/jetty/docked-vessel',
          permission: 'IdentityServer.OpenIddictScopes',
        },
      ],
    },
    {
      title: 'Master Data',
      url: '#',
      isActive: false,
      icon: IconDatabase,
      permission: 'IdentityServer.OpenIddictApplications',
      items: [
        {
          title: 'Manage Jetty',
          url: '/jetty',
          permission: 'IdentityServer.OpenIddictApplications',
        },
      ],
    },
    {
      title: 'Reports',
      url: '/report',
      isActive: false,
      icon: IconReportAnalytics,
      permission: 'IdentityServer.ClaimTypes',
    },

    {
      title: 'Document Template',
      url: '/document-template',
      isActive: false,
      icon: IconReportAnalytics,
      permission: 'IdentityServer.ClaimTypes',
    },
    
    {
      title: 'Approval Template',
      url: '/approval-template',
      isActive: false,
      icon: IconReportAnalytics,
      permission: 'IdentityServer.ClaimTypes',
    },

  ]
