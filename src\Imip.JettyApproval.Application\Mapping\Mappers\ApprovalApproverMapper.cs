using System;
using System.Collections.Generic;
using Imip.JettyApproval.Approvals.ApprovalApprovers;
using Riok.Mapperly.Abstractions;

namespace Imip.JettyApproval.Mapping.Mappers;

/// <summary>
/// Mapper for ApprovalApprover entity
/// </summary>
[Mapper]
public partial class ApprovalApproverMapper : IMapperlyMapper
{
    // Entity to DTO mapping
    [MapProperty(nameof(ApprovalApprover.Id), nameof(ApprovalApproverDto.Id))]
    [MapperIgnoreSource(nameof(ApprovalApprover.IsDeleted))]
    [MapperIgnoreSource(nameof(ApprovalApprover.DeleterId))]
    [MapperIgnoreSource(nameof(ApprovalApprover.DeletionTime))]
    [MapperIgnoreSource(nameof(ApprovalApprover.LastModificationTime))]
    [MapperIgnoreSource(nameof(ApprovalApprover.LastModifierId))]
    [MapperIgnoreSource(nameof(ApprovalApprover.CreationTime))]
    [MapperIgnoreSource(nameof(ApprovalApprover.CreatorId))]
    [MapperIgnoreSource(nameof(ApprovalApprover.ExtraProperties))]
    [MapperIgnoreSource(nameof(ApprovalApprover.ConcurrencyStamp))]
    public partial ApprovalApproverDto MapToDto(ApprovalApprover entity);

    // DTO to Entity mapping for updates (maps to existing entity)
    [MapperIgnoreTarget(nameof(ApprovalApprover.Id))] // Don't change existing Id
    [MapperIgnoreSource(nameof(CreateUpdateApprovalApproverDto.ApprovalId))] // Ignore ApprovalId from DTO
    public partial void MapToEntity(CreateUpdateApprovalApproverDto dto, ApprovalApprover entity);

    // Custom mapping methods for complex scenarios
    public ApprovalApprover CreateEntityWithId(CreateUpdateApprovalApproverDto dto, Guid id)
    {
        // Create entity using reflection since constructor is protected
        var entity = (ApprovalApprover)Activator.CreateInstance(typeof(ApprovalApprover), true)!;

        // Map properties manually
        MapToEntity(dto, entity);

        return entity;
    }

    // Map list of entities to DTOs
    public partial List<ApprovalApproverDto> MapToDtoList(List<ApprovalApprover> entities);

    // Map IEnumerable for LINQ scenarios
    public partial IEnumerable<ApprovalApproverDto> MapToDtoEnumerable(IEnumerable<ApprovalApprover> entities);
}