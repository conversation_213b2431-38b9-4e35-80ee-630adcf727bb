using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Attachments;

/// <summary>
/// Application service interface for attachment operations
/// </summary>
public interface IAttachmentAppService : IApplicationService
{
    /// <summary>
    /// Uploads a file
    /// </summary>
    /// <param name="input">The file uploads DTO with metadata</param>
    /// <param name="fileName">The name of the file</param>
    /// <param name="contentType">The content type of the file</param>
    /// <param name="fileBytes">The file content as a byte array</param>
    /// <returns>The file upload result DTO</returns>
    Task<FileUploadResultDto> UploadFileAsync(FileUploadDto input, string fileName, string contentType, byte[] fileBytes);
}