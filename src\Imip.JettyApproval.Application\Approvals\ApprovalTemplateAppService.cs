using Imip.JettyApproval.Approvals;
using Imip.JettyApproval.Approvals.ApprovalTemplates;
using Imip.JettyApproval.Approvals.ApprovalApprovers;
using Imip.JettyApproval.Approvals.ApprovalCriterias;
using Imip.JettyApproval.Approvals.ApprovalStages;
using Imip.JettyApproval.Mapping.Mappers;
using Imip.JettyApproval.Models;
using Imip.JettyApproval.Permissions.Apps;
using Imip.JettyApproval.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Approvals;

/// <summary>
/// Application service for ApprovalTemplate entity
/// </summary>
[Authorize(JettyApprovalPermission.PolicyApprovalTemplate.Default)]
public class ApprovalTemplateAppService :
    CrudAppService<ApprovalTemplate, ApprovalTemplateDto, Guid, PagedAndSortedResultRequestDto, CreateUpdateApprovalTemplateDto, CreateUpdateApprovalTemplateDto>,
    IApprovalTemplateAppService
{
    private readonly IApprovalTemplateRepository _approvalTemplateRepository;
    private readonly IApprovalApproverRepository _approvalApproverRepository;
    private readonly IApprovalCriteriaRepository _approvalCriteriaRepository;
    private readonly IApprovalStageRepository _approvalStageRepository;
    private readonly ApprovalTemplateMapper _mapper;
    private readonly ApprovalApproverMapper _approverMapper;
    private readonly ApprovalCriteriaMapper _criteriaMapper;
    private readonly ApprovalStageMapper _stageMapper;
    private readonly ILogger<ApprovalTemplateAppService> _logger;

    public ApprovalTemplateAppService(
        IApprovalTemplateRepository approvalTemplateRepository,
        IApprovalApproverRepository approvalApproverRepository,
        IApprovalCriteriaRepository approvalCriteriaRepository,
        IApprovalStageRepository approvalStageRepository,
        ApprovalTemplateMapper mapper,
        ApprovalApproverMapper approverMapper,
        ApprovalCriteriaMapper criteriaMapper,
        ApprovalStageMapper stageMapper,
        ILogger<ApprovalTemplateAppService> logger)
        : base(approvalTemplateRepository)
    {
        _approvalTemplateRepository = approvalTemplateRepository;
        _approvalApproverRepository = approvalApproverRepository;
        _approvalCriteriaRepository = approvalCriteriaRepository;
        _approvalStageRepository = approvalStageRepository;
        _mapper = mapper;
        _approverMapper = approverMapper;
        _criteriaMapper = criteriaMapper;
        _stageMapper = stageMapper;
        _logger = logger;
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalTemplate.Create)]
    public override async Task<ApprovalTemplateDto> CreateAsync(CreateUpdateApprovalTemplateDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = _mapper.CreateEntityWithId(input, Guid.NewGuid());

        // The mapper already creates the related entities through navigation properties
        // We just need to set the ApprovalId for each related entity
        foreach (var approver in entity.Approvers)
        {
            approver.ApprovalId = entity.Id;
        }
        foreach (var criteria in entity.Criterias)
        {
            criteria.ApprovalId = entity.Id;
        }

        await _approvalTemplateRepository.InsertAsync(entity, autoSave: true);

        return _mapper.MapToDto(entity);
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalTemplate.Edit)]
    public override async Task<ApprovalTemplateDto> UpdateAsync(Guid id, CreateUpdateApprovalTemplateDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await _approvalTemplateRepository.GetAsync(id);

        _mapper.MapToEntity(input, entity);

        await _approvalTemplateRepository.UpdateAsync(entity, autoSave: true);

        // Handle related entities
        await HandleApproversAsync(entity.Id, input.Approvers);
        await HandleCriteriasAsync(entity.Id, input.Criterias);
        // await HandleStagesAsync(entity.Id, input.Stages);

        return _mapper.MapToDto(entity);
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalTemplate.Delete)]
    public override async Task DeleteAsync(Guid id)
    {
        await CheckDeletePolicyAsync();

        var entity = await _approvalTemplateRepository.GetAsync(id);

        await _approvalTemplateRepository.DeleteAsync(entity, autoSave: true);
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalTemplate.View)]
    public override async Task<ApprovalTemplateDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var entity = await _approvalTemplateRepository.GetAsync(id);
        var dto = _mapper.MapToDto(entity);

        // Load related entities
        dto.Approvers = await LoadApproversAsync(id);
        dto.Criterias = await LoadCriteriasAsync(id);
        dto.Stages = await LoadStagesAsync(id);

        return dto;
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalTemplate.View)]
    public override async Task<PagedResultDto<ApprovalTemplateDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        await CheckGetListPolicyAsync();

        var queryable = await _approvalTemplateRepository.GetQueryableAsync();

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.Sorting))
        {
            queryable = queryable.OrderBy(input.Sorting);
        }
        else
        {
            queryable = queryable.OrderByDescending(x => x.CreationTime);
        }

        var totalCount = await AsyncExecuter.CountAsync(queryable);

        var entities = await AsyncExecuter.ToListAsync(
            queryable.PageBy(input.SkipCount, input.MaxResultCount)
        );

        var dtos = entities.Select(_mapper.MapToDto).ToList();

        return new PagedResultDto<ApprovalTemplateDto>(totalCount, dtos);
    }

    [Authorize(JettyApprovalPermission.PolicyApprovalTemplate.View)]
    public virtual async Task<PagedResultDto<ApprovalTemplateDto>> FilterListAsync(QueryParametersDto parameters)
    {
        var query = await _approvalTemplateRepository.GetQueryableAsync();
        query = ApplyDynamicQuery(query, parameters);
        var totalCount = await AsyncExecuter.CountAsync(query);
        var items = await AsyncExecuter.ToListAsync(
            query.Skip(parameters.SkipCount).Take(parameters.MaxResultCount)
        );
        var dtos = items.Select(_mapper.MapToDto).ToList();
        return new PagedResultDto<ApprovalTemplateDto>
        {
            TotalCount = totalCount,
            Items = dtos
        };
    }

    private IQueryable<ApprovalTemplate> ApplyDynamicQuery(IQueryable<ApprovalTemplate> query, QueryParametersDto parameters)
    {
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ApprovalTemplate>.ApplyFilters(query, parameters.FilterGroup);
        }
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ApprovalTemplate>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime);
        }
        return query;
    }

    private async Task HandleApproversAsync(Guid approvalTemplateId, List<CreateUpdateApprovalApproverDto> approvers)
    {
        if (approvers == null || !approvers.Any())
            return;

        foreach (var approverDto in approvers)
        {
            if (approverDto.Id != Guid.Empty)
            {
                // Update existing approver
                var existingApprover = await _approvalApproverRepository.GetAsync(approverDto.Id.Value);
                _approverMapper.MapToEntity(approverDto, existingApprover);
                existingApprover.ApprovalId = approvalTemplateId; // Ensure correct foreign key
                await _approvalApproverRepository.UpdateAsync(existingApprover, autoSave: false);
            }
            else
            {
                // Create new approver
                var approverEntity = _approverMapper.CreateEntityWithId(approverDto, Guid.NewGuid());
                approverEntity.ApprovalId = approvalTemplateId; // Ensure correct foreign key
                await _approvalApproverRepository.InsertAsync(approverEntity, autoSave: false);
            }
        }
    }

    private async Task HandleCriteriasAsync(Guid approvalTemplateId, List<CreateUpdateApprovalCriteriaDto> criterias)
    {
        if (criterias == null || !criterias.Any())
            return;

        foreach (var criteriaDto in criterias)
        {
            if (criteriaDto.Id != Guid.Empty)
            {
                // Update existing criteria
                var existingCriteria = await _approvalCriteriaRepository.GetAsync(criteriaDto.Id.Value);
                _criteriaMapper.MapToEntity(criteriaDto, existingCriteria);
                existingCriteria.ApprovalId = approvalTemplateId; // Ensure correct foreign key
                await _approvalCriteriaRepository.UpdateAsync(existingCriteria, autoSave: false);
            }
            else
            {
                // Create new criteria
                var criteriaEntity = _criteriaMapper.CreateEntityWithId(criteriaDto, Guid.NewGuid());
                criteriaEntity.ApprovalId = approvalTemplateId; // Ensure correct foreign key
                await _approvalCriteriaRepository.InsertAsync(criteriaEntity, autoSave: false);
            }
        }
    }

    private async Task HandleStagesAsync(Guid approvalTemplateId, List<CreateUpdateApprovalStageDto> stages)
    {
        if (stages == null || !stages.Any())
            return;

        foreach (var stageDto in stages)
        {
            if (stageDto.Id != Guid.Empty)
            {
                // Update existing stage
                var existingStage = await _approvalStageRepository.GetAsync(stageDto.Id);
                _stageMapper.MapToEntity(stageDto, existingStage);
                existingStage.ApprovalTemplateId = approvalTemplateId; // Ensure correct foreign key
                await _approvalStageRepository.UpdateAsync(existingStage, autoSave: false);
            }
            else
            {
                // Create new stage
                var stageEntity = _stageMapper.CreateEntityWithId(stageDto, Guid.NewGuid());
                stageEntity.ApprovalTemplateId = approvalTemplateId; // Ensure correct foreign key
                await _approvalStageRepository.InsertAsync(stageEntity, autoSave: false);
            }
        }
    }

    private async Task<List<ApprovalApproverDto>> LoadApproversAsync(Guid approvalTemplateId)
    {
        var approvers = await _approvalApproverRepository.GetByApprovalIdAsync(approvalTemplateId);
        return approvers.Select(_approverMapper.MapToDto).ToList();
    }

    private async Task<List<ApprovalCriteriaDto>> LoadCriteriasAsync(Guid approvalTemplateId)
    {
        var criterias = await _approvalCriteriaRepository.GetByApprovalIdAsync(approvalTemplateId);
        return criterias.Select(_criteriaMapper.MapToDto).ToList();
    }

    private async Task<List<ApprovalStageDto>> LoadStagesAsync(Guid approvalTemplateId)
    {
        var stages = await _approvalStageRepository.GetByApprovalTemplateIdAsync(approvalTemplateId);
        return stages.Select(_stageMapper.MapToDto).ToList();
    }
}