using System;
using System.Threading.Tasks;
using Imip.JettyApproval.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.JettyApproval.Approvals.ApprovalStages;

/// <summary>
/// Application service interface for ApprovalStage entity
/// </summary>
public interface IApprovalStageAppService :
    ICrudAppService<
        ApprovalStageDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateApprovalStageDto,
        CreateUpdateApprovalStageDto>
{
    Task<PagedResultDto<ApprovalStageDto>> FilterListAsync(QueryParametersDto parameters);
}